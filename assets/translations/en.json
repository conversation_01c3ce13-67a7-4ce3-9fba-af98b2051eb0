{"app_title": "Health Diary", "good_morning": "Good Morning", "today_health": "Today's Health", "blood_pressure": "BP", "blood_sugar": "BG", "recent_records": "Recent Records", "settings": "Settings", "statistics": "Statistics", "navigate_to_statistics": "Navigate to Statistics", "navigate_to_settings": "Navigate to Settings", "language": "Language", "select_language": "Select Language", "cancel": "Cancel", "chinese": "中文", "english": "English", "status_good": "Good", "today_time": "Today 08:30", "yesterday_evening": "Yesterday 18:45", "yesterday_morning": "Yesterday 08:15", "morning_measurement_note": "Morning measurement, rested 5 minutes, sitting position", "after_dinner_note": "2 hours after dinner, had cake today", "wake_up_note": "Measured immediately after waking up, may be high", "systolic": "SYS", "diastolic": "DIA", "pulse": "Pulse", "add_blood_pressure": "Add Blood Pressure", "add_blood_sugar": "Add Blood Sugar", "scan_blood_pressure": "Scan Blood Pressure Monitor", "scan_blood_sugar": "Scan Blood Glucose Meter", "scan_blood_pressure_desc": "Point camera at blood pressure monitor screen to auto-detect values", "scan_blood_sugar_desc": "Point camera at blood glucose meter screen to auto-detect values", "start_scan": "<PERSON>an", "recognize": "Recognize", "or_manual_input": "or Manual Input", "manual_input": "Manual Input", "systolic_pressure": "Systolic Pressure", "diastolic_pressure": "Diastolic Pressure", "pulse_optional": "Pulse (Optional)", "note_optional": "Note (Optional)", "add_note_hint": "Add measurement conditions...", "blood_sugar_value": "Blood Sugar Value", "blood_sugar_reference": "Normal range: Fasting 3.9-6.1, 2h post-meal <7.8 mmol/L", "save_record": "Save Record", "required_field": "This field is required", "invalid_systolic": "Systolic should be between 50-300", "invalid_diastolic": "Diastolic should be between 30-200", "invalid_pulse": "Pulse should be between 30-200", "invalid_blood_sugar": "Blood sugar should be between 1.0-30.0", "scan_failed": "<PERSON><PERSON> failed, please try again", "no_records_yet": "No records yet", "today": "Today", "yesterday": "Yesterday", "retry": "Retry", "save_success": "Record saved successfully", "save_failed": "Failed to save record", "retake": "Retake", "confirm": "Confirm", "camera_permission_required": "Camera Permission Required", "camera_permission_denied": "Camera Permission Denied", "camera_access_restricted": "Camera Access Restricted", "camera_initialization_failed": "Camera Initialization Failed", "camera_error": "Camera Error", "no_camera_available": "No Camera Available", "time": "Time", "select_date_time": "Select date and time", "enter_value": "Enter value", "times_per_minute": "times/min", "mmhg": "mmHg", "mmol_l": "mmol/L", "back_to_scan": "Back to Scan", "record_saved": "Record saved", "add_record": "Add Record", "error_loading_types": "Error loading record types", "go_to_camera_permission_settings": "Go to Camera Permission Settings", "bloodPressure": "Blood Pressure", "bloodSugar": "Blood Sugar", "scan_recognition_failed": "Recognition failed, please aim at device screen", "no_records_today": "No records today", "health_status_good": "Good", "health_status_attention": "Attention", "health_status_fair": "Fair", "no_record_today": "No record", "today_health_overview": "Today's Health Overview"}