// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

/// Flutter widgets implementing the current iOS design language.
///
/// To use, import `package:flutter/cupertino.dart`.
///
/// This library is designed for apps that run on iOS. For apps that may also
/// run on other operating systems, we encourage use of other widgets, for
/// example the [Material
/// Design](https://docs.flutter.dev/ui/widgets/material) set.
///
/// {@youtube 560 315 https://www.youtube.com/watch?v=3PdUaidHc-E}
///
/// See also:
///
///  * [flutter.dev/widgets/cupertino](https://docs.flutter.dev/ui/widgets/cupertino)
///    for a catalog of all Cupertino widgets.
///  * [flutter.dev/widgets](https://docs.flutter.dev/ui/widgets)
///    for a catalog of commonly-used Flutter widgets.

library cupertino;

export 'src/cupertino/activity_indicator.dart';
export 'src/cupertino/adaptive_text_selection_toolbar.dart';
export 'src/cupertino/app.dart';
export 'src/cupertino/bottom_tab_bar.dart';
export 'src/cupertino/button.dart';
export 'src/cupertino/checkbox.dart';
export 'src/cupertino/colors.dart';
export 'src/cupertino/constants.dart';
export 'src/cupertino/context_menu.dart';
export 'src/cupertino/context_menu_action.dart';
export 'src/cupertino/date_picker.dart';
export 'src/cupertino/debug.dart';
export 'src/cupertino/desktop_text_selection.dart';
export 'src/cupertino/desktop_text_selection_toolbar.dart';
export 'src/cupertino/desktop_text_selection_toolbar_button.dart';
export 'src/cupertino/dialog.dart';
export 'src/cupertino/form_row.dart';
export 'src/cupertino/form_section.dart';
export 'src/cupertino/icon_theme_data.dart';
export 'src/cupertino/icons.dart';
export 'src/cupertino/interface_level.dart';
export 'src/cupertino/list_section.dart';
export 'src/cupertino/list_tile.dart';
export 'src/cupertino/localizations.dart';
export 'src/cupertino/magnifier.dart';
export 'src/cupertino/nav_bar.dart';
export 'src/cupertino/page_scaffold.dart';
export 'src/cupertino/picker.dart';
export 'src/cupertino/radio.dart';
export 'src/cupertino/refresh.dart';
export 'src/cupertino/route.dart';
export 'src/cupertino/scrollbar.dart';
export 'src/cupertino/search_field.dart';
export 'src/cupertino/segmented_control.dart';
export 'src/cupertino/sheet.dart';
export 'src/cupertino/slider.dart';
export 'src/cupertino/sliding_segmented_control.dart';
export 'src/cupertino/spell_check_suggestions_toolbar.dart';
export 'src/cupertino/switch.dart';
export 'src/cupertino/tab_scaffold.dart';
export 'src/cupertino/tab_view.dart';
export 'src/cupertino/text_field.dart';
export 'src/cupertino/text_form_field_row.dart';
export 'src/cupertino/text_selection.dart';
export 'src/cupertino/text_selection_toolbar.dart';
export 'src/cupertino/text_selection_toolbar_button.dart';
export 'src/cupertino/text_theme.dart';
export 'src/cupertino/theme.dart';
export 'src/cupertino/thumb_painter.dart';
export 'widgets.dart';
