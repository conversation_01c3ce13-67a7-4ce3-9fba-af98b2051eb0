// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'health_types.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
HealthRecordData _$HealthRecordDataFromJson(
  Map<String, dynamic> json
) {
        switch (json['runtimeType']) {
                  case 'bloodPressure':
          return BloodPressureData.fromJson(
            json
          );
                case 'bloodSugar':
          return BloodSugarData.fromJson(
            json
          );
        
          default:
            throw CheckedFromJsonException(
  json,
  'runtimeType',
  'HealthRecordData',
  'Invalid union type "${json['runtimeType']}"!'
);
        }
      
}

/// @nodoc
mixin _$HealthRecordData {

 String? get note;
/// Create a copy of HealthRecordData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$HealthRecordDataCopyWith<HealthRecordData> get copyWith => _$HealthRecordDataCopyWithImpl<HealthRecordData>(this as HealthRecordData, _$identity);

  /// Serializes this HealthRecordData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is HealthRecordData&&(identical(other.note, note) || other.note == note));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,note);

@override
String toString() {
  return 'HealthRecordData(note: $note)';
}


}

/// @nodoc
abstract mixin class $HealthRecordDataCopyWith<$Res>  {
  factory $HealthRecordDataCopyWith(HealthRecordData value, $Res Function(HealthRecordData) _then) = _$HealthRecordDataCopyWithImpl;
@useResult
$Res call({
 String? note
});




}
/// @nodoc
class _$HealthRecordDataCopyWithImpl<$Res>
    implements $HealthRecordDataCopyWith<$Res> {
  _$HealthRecordDataCopyWithImpl(this._self, this._then);

  final HealthRecordData _self;
  final $Res Function(HealthRecordData) _then;

/// Create a copy of HealthRecordData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? note = freezed,}) {
  return _then(_self.copyWith(
note: freezed == note ? _self.note : note // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class BloodPressureData implements HealthRecordData {
  const BloodPressureData({required this.systolic, required this.diastolic, this.pulse, this.note, final  String? $type}): $type = $type ?? 'bloodPressure';
  factory BloodPressureData.fromJson(Map<String, dynamic> json) => _$BloodPressureDataFromJson(json);

 final  int systolic;
 final  int diastolic;
 final  int? pulse;
@override final  String? note;

@JsonKey(name: 'runtimeType')
final String $type;


/// Create a copy of HealthRecordData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BloodPressureDataCopyWith<BloodPressureData> get copyWith => _$BloodPressureDataCopyWithImpl<BloodPressureData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BloodPressureDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BloodPressureData&&(identical(other.systolic, systolic) || other.systolic == systolic)&&(identical(other.diastolic, diastolic) || other.diastolic == diastolic)&&(identical(other.pulse, pulse) || other.pulse == pulse)&&(identical(other.note, note) || other.note == note));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,systolic,diastolic,pulse,note);

@override
String toString() {
  return 'HealthRecordData.bloodPressure(systolic: $systolic, diastolic: $diastolic, pulse: $pulse, note: $note)';
}


}

/// @nodoc
abstract mixin class $BloodPressureDataCopyWith<$Res> implements $HealthRecordDataCopyWith<$Res> {
  factory $BloodPressureDataCopyWith(BloodPressureData value, $Res Function(BloodPressureData) _then) = _$BloodPressureDataCopyWithImpl;
@override @useResult
$Res call({
 int systolic, int diastolic, int? pulse, String? note
});




}
/// @nodoc
class _$BloodPressureDataCopyWithImpl<$Res>
    implements $BloodPressureDataCopyWith<$Res> {
  _$BloodPressureDataCopyWithImpl(this._self, this._then);

  final BloodPressureData _self;
  final $Res Function(BloodPressureData) _then;

/// Create a copy of HealthRecordData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? systolic = null,Object? diastolic = null,Object? pulse = freezed,Object? note = freezed,}) {
  return _then(BloodPressureData(
systolic: null == systolic ? _self.systolic : systolic // ignore: cast_nullable_to_non_nullable
as int,diastolic: null == diastolic ? _self.diastolic : diastolic // ignore: cast_nullable_to_non_nullable
as int,pulse: freezed == pulse ? _self.pulse : pulse // ignore: cast_nullable_to_non_nullable
as int?,note: freezed == note ? _self.note : note // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc
@JsonSerializable()

class BloodSugarData implements HealthRecordData {
  const BloodSugarData({required this.value, this.note, final  String? $type}): $type = $type ?? 'bloodSugar';
  factory BloodSugarData.fromJson(Map<String, dynamic> json) => _$BloodSugarDataFromJson(json);

 final  double value;
@override final  String? note;

@JsonKey(name: 'runtimeType')
final String $type;


/// Create a copy of HealthRecordData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BloodSugarDataCopyWith<BloodSugarData> get copyWith => _$BloodSugarDataCopyWithImpl<BloodSugarData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BloodSugarDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BloodSugarData&&(identical(other.value, value) || other.value == value)&&(identical(other.note, note) || other.note == note));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,value,note);

@override
String toString() {
  return 'HealthRecordData.bloodSugar(value: $value, note: $note)';
}


}

/// @nodoc
abstract mixin class $BloodSugarDataCopyWith<$Res> implements $HealthRecordDataCopyWith<$Res> {
  factory $BloodSugarDataCopyWith(BloodSugarData value, $Res Function(BloodSugarData) _then) = _$BloodSugarDataCopyWithImpl;
@override @useResult
$Res call({
 double value, String? note
});




}
/// @nodoc
class _$BloodSugarDataCopyWithImpl<$Res>
    implements $BloodSugarDataCopyWith<$Res> {
  _$BloodSugarDataCopyWithImpl(this._self, this._then);

  final BloodSugarData _self;
  final $Res Function(BloodSugarData) _then;

/// Create a copy of HealthRecordData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? value = null,Object? note = freezed,}) {
  return _then(BloodSugarData(
value: null == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as double,note: freezed == note ? _self.note : note // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc
mixin _$BloodSugarRecord {

 double get value;
/// Create a copy of BloodSugarRecord
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BloodSugarRecordCopyWith<BloodSugarRecord> get copyWith => _$BloodSugarRecordCopyWithImpl<BloodSugarRecord>(this as BloodSugarRecord, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BloodSugarRecord&&(identical(other.value, value) || other.value == value));
}


@override
int get hashCode => Object.hash(runtimeType,value);

@override
String toString() {
  return 'BloodSugarRecord(value: $value)';
}


}

/// @nodoc
abstract mixin class $BloodSugarRecordCopyWith<$Res>  {
  factory $BloodSugarRecordCopyWith(BloodSugarRecord value, $Res Function(BloodSugarRecord) _then) = _$BloodSugarRecordCopyWithImpl;
@useResult
$Res call({
 double value
});




}
/// @nodoc
class _$BloodSugarRecordCopyWithImpl<$Res>
    implements $BloodSugarRecordCopyWith<$Res> {
  _$BloodSugarRecordCopyWithImpl(this._self, this._then);

  final BloodSugarRecord _self;
  final $Res Function(BloodSugarRecord) _then;

/// Create a copy of BloodSugarRecord
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? value = null,}) {
  return _then(_self.copyWith(
value: null == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// @nodoc


class _BloodSugarRecord implements BloodSugarRecord {
  const _BloodSugarRecord({required this.value});
  

@override final  double value;

/// Create a copy of BloodSugarRecord
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BloodSugarRecordCopyWith<_BloodSugarRecord> get copyWith => __$BloodSugarRecordCopyWithImpl<_BloodSugarRecord>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BloodSugarRecord&&(identical(other.value, value) || other.value == value));
}


@override
int get hashCode => Object.hash(runtimeType,value);

@override
String toString() {
  return 'BloodSugarRecord(value: $value)';
}


}

/// @nodoc
abstract mixin class _$BloodSugarRecordCopyWith<$Res> implements $BloodSugarRecordCopyWith<$Res> {
  factory _$BloodSugarRecordCopyWith(_BloodSugarRecord value, $Res Function(_BloodSugarRecord) _then) = __$BloodSugarRecordCopyWithImpl;
@override @useResult
$Res call({
 double value
});




}
/// @nodoc
class __$BloodSugarRecordCopyWithImpl<$Res>
    implements _$BloodSugarRecordCopyWith<$Res> {
  __$BloodSugarRecordCopyWithImpl(this._self, this._then);

  final _BloodSugarRecord _self;
  final $Res Function(_BloodSugarRecord) _then;

/// Create a copy of BloodSugarRecord
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? value = null,}) {
  return _then(_BloodSugarRecord(
value: null == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

/// @nodoc
mixin _$BloodPressureRecord {

 int get systolic; int get diastolic; int? get pulse;
/// Create a copy of BloodPressureRecord
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BloodPressureRecordCopyWith<BloodPressureRecord> get copyWith => _$BloodPressureRecordCopyWithImpl<BloodPressureRecord>(this as BloodPressureRecord, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BloodPressureRecord&&(identical(other.systolic, systolic) || other.systolic == systolic)&&(identical(other.diastolic, diastolic) || other.diastolic == diastolic)&&(identical(other.pulse, pulse) || other.pulse == pulse));
}


@override
int get hashCode => Object.hash(runtimeType,systolic,diastolic,pulse);

@override
String toString() {
  return 'BloodPressureRecord(systolic: $systolic, diastolic: $diastolic, pulse: $pulse)';
}


}

/// @nodoc
abstract mixin class $BloodPressureRecordCopyWith<$Res>  {
  factory $BloodPressureRecordCopyWith(BloodPressureRecord value, $Res Function(BloodPressureRecord) _then) = _$BloodPressureRecordCopyWithImpl;
@useResult
$Res call({
 int systolic, int diastolic, int? pulse
});




}
/// @nodoc
class _$BloodPressureRecordCopyWithImpl<$Res>
    implements $BloodPressureRecordCopyWith<$Res> {
  _$BloodPressureRecordCopyWithImpl(this._self, this._then);

  final BloodPressureRecord _self;
  final $Res Function(BloodPressureRecord) _then;

/// Create a copy of BloodPressureRecord
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? systolic = null,Object? diastolic = null,Object? pulse = freezed,}) {
  return _then(_self.copyWith(
systolic: null == systolic ? _self.systolic : systolic // ignore: cast_nullable_to_non_nullable
as int,diastolic: null == diastolic ? _self.diastolic : diastolic // ignore: cast_nullable_to_non_nullable
as int,pulse: freezed == pulse ? _self.pulse : pulse // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc


class _BloodPressureRecord implements BloodPressureRecord {
  const _BloodPressureRecord({required this.systolic, required this.diastolic, this.pulse});
  

@override final  int systolic;
@override final  int diastolic;
@override final  int? pulse;

/// Create a copy of BloodPressureRecord
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BloodPressureRecordCopyWith<_BloodPressureRecord> get copyWith => __$BloodPressureRecordCopyWithImpl<_BloodPressureRecord>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BloodPressureRecord&&(identical(other.systolic, systolic) || other.systolic == systolic)&&(identical(other.diastolic, diastolic) || other.diastolic == diastolic)&&(identical(other.pulse, pulse) || other.pulse == pulse));
}


@override
int get hashCode => Object.hash(runtimeType,systolic,diastolic,pulse);

@override
String toString() {
  return 'BloodPressureRecord(systolic: $systolic, diastolic: $diastolic, pulse: $pulse)';
}


}

/// @nodoc
abstract mixin class _$BloodPressureRecordCopyWith<$Res> implements $BloodPressureRecordCopyWith<$Res> {
  factory _$BloodPressureRecordCopyWith(_BloodPressureRecord value, $Res Function(_BloodPressureRecord) _then) = __$BloodPressureRecordCopyWithImpl;
@override @useResult
$Res call({
 int systolic, int diastolic, int? pulse
});




}
/// @nodoc
class __$BloodPressureRecordCopyWithImpl<$Res>
    implements _$BloodPressureRecordCopyWith<$Res> {
  __$BloodPressureRecordCopyWithImpl(this._self, this._then);

  final _BloodPressureRecord _self;
  final $Res Function(_BloodPressureRecord) _then;

/// Create a copy of BloodPressureRecord
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? systolic = null,Object? diastolic = null,Object? pulse = freezed,}) {
  return _then(_BloodPressureRecord(
systolic: null == systolic ? _self.systolic : systolic // ignore: cast_nullable_to_non_nullable
as int,diastolic: null == diastolic ? _self.diastolic : diastolic // ignore: cast_nullable_to_non_nullable
as int,pulse: freezed == pulse ? _self.pulse : pulse // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

// dart format on
