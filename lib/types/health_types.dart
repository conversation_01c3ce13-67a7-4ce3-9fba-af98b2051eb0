import 'package:freezed_annotation/freezed_annotation.dart';

part 'health_types.freezed.dart';
part 'health_types.g.dart';

/// 健康记录数据联合类型
@freezed
class HealthRecordData with _$HealthRecordData {
  /// 血压记录
  const factory HealthRecordData.bloodPressure({
    required int systolic,
    required int diastolic,
    int? pulse,
    String? note,
  }) = BloodPressureData;

  /// 血糖记录
  const factory HealthRecordData.bloodSugar({
    required double value,
    String? note,
  }) = BloodSugarData;

  factory HealthRecordData.fromJson(Map<String, dynamic> json) => _$HealthRecordDataFromJson(json);
}

/// 兼容性：保留原有的 BloodSugarRecord 类型
@freezed
abstract class BloodSugarRecord with _$BloodSugarRecord {
  const factory BloodSugarRecord({required double value}) = _BloodSugarRecord;
}

/// 兼容性：保留原有的 BloodPressureRecord 类型
@freezed
abstract class BloodPressureRecord with _$BloodPressureRecord {
  const factory BloodPressureRecord({required int systolic, required int diastolic, int? pulse}) = _BloodPressureRecord;
}

/// 健康记录类型
enum HealthRecordTypeEnum {
  bloodPressure,
  bloodSugar;

  static HealthRecordTypeEnum fromInt(int i) {
    return HealthRecordTypeEnum.values[i];
  }

  int toInt() {
    return index;
  }
}
