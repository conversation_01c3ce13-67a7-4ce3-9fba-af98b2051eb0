import 'package:health_diary/types/health_types.dart';

/// 健康记录条目
class HealthRecordEntry {
  final String time;
  final String type;
  final String note;
  final HealthRecordTypeEnum recordType;
  final List<HealthRecordValue> values;
  final int color;

  const HealthRecordEntry({
    required this.time,
    required this.type,
    required this.note,
    required this.recordType,
    required this.values,
    required this.color,
  });
}

/// 健康记录数值
class HealthRecordValue {
  final String label;
  final String value;
  final String unit;

  const HealthRecordValue({required this.label, required this.value, required this.unit});
}

/// 今日健康概览
class TodayHealthOverview {
  final String status;
  final String? bloodPressure;  // 可能为空，表示无记录
  final String? bloodSugar;     // 可能为空，表示无记录
  final String bloodPressureUnit;
  final String bloodSugarUnit;
  final bool hasBloodPressureRecord;
  final bool hasBloodSugarRecord;

  const TodayHealthOverview({
    required this.status,
    this.bloodPressure,
    this.bloodSugar,
    this.bloodPressureUnit = 'mmHg',
    this.bloodSugarUnit = 'mmol/L',
    this.hasBloodPressureRecord = false,
    this.hasBloodSugarRecord = false,
  });

  /// 创建无记录状态
  factory TodayHealthOverview.noRecords() {
    return const TodayHealthOverview(
      status: '暂无数据',
      hasBloodPressureRecord: false,
      hasBloodSugarRecord: false,
    );
  }

  /// 创建有记录状态
  factory TodayHealthOverview.withRecords({
    required String status,
    String? bloodPressure,
    String? bloodSugar,
    bool hasBloodPressureRecord = false,
    bool hasBloodSugarRecord = false,
  }) {
    return TodayHealthOverview(
      status: status,
      bloodPressure: bloodPressure,
      bloodSugar: bloodSugar,
      hasBloodPressureRecord: hasBloodPressureRecord,
      hasBloodSugarRecord: hasBloodSugarRecord,
    );
  }
}
