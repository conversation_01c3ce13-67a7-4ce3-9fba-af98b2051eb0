import 'dart:convert';
import 'package:drift/drift.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../model/health_record.dart';
import '../providers/database_provider.dart';
import '../types/health_types.dart';
import 'database.dart';

part 'health_record_repository.g.dart';

/// 统一健康记录仓库
class HealthRecordRepository {
  final HealthDiaryDatabase _database;

  HealthRecordRepository(this._database);

  /// 获取所有健康记录
  Stream<List<HealthRecordRow>> getAllHealthRecords() {
    return (_database.select(_database.healthRecords)
          ..orderBy([(t) => OrderingTerm.desc(t.createdAt)]))
        .watch();
  }

  /// 根据ID获取健康记录
  Future<HealthRecordRow?> getHealthRecordById(int id) {
    return (_database.select(_database.healthRecords)
          ..where((t) => t.id.equals(id)))
        .getSingleOrNull();
  }

  /// 添加血压记录
  Future<int> addBloodPressureRecord({
    required int systolic,
    required int diastolic,
    int? pulse,
    String? note,
    required DateTime createdAt,
  }) {
    final data = HealthRecordData.bloodPressure(
      systolic: systolic,
      diastolic: diastolic,
      pulse: pulse,
      note: note,
    );

    final companion = HealthRecordsCompanion(
      type: Value(HealthRecordTypeEnum.bloodPressure.toInt()),
      data: Value(json.encode(data.toJson())),
      createdAt: Value(createdAt),
    );

    return _database.into(_database.healthRecords).insert(companion);
  }

  /// 添加血糖记录
  Future<int> addBloodSugarRecord({
    required double value,
    String? note,
    required DateTime createdAt,
  }) {
    final data = HealthRecordData.bloodSugar(
      value: value,
      note: note,
    );

    final companion = HealthRecordsCompanion(
      type: Value(HealthRecordTypeEnum.bloodSugar.toInt()),
      data: Value(json.encode(data.toJson())),
      createdAt: Value(createdAt),
    );

    return _database.into(_database.healthRecords).insert(companion);
  }

  /// 更新健康记录
  Future<bool> updateHealthRecord(HealthRecordRow record) {
    final companion = HealthRecordsCompanion(
      id: Value(record.id),
      type: Value(record.type.toInt()),
      data: Value(json.encode(record.model.toJson())),
      createdAt: Value(record.createdAt),
    );
    return _database.update(_database.healthRecords).replace(companion);
  }

  /// 删除健康记录
  Future<int> deleteHealthRecord(int id) {
    return (_database.delete(_database.healthRecords)
          ..where((t) => t.id.equals(id)))
        .go();
  }

  /// 批量删除健康记录
  Future<int> deleteHealthRecords(List<int> ids) {
    return (_database.delete(_database.healthRecords)
          ..where((t) => t.id.isIn(ids)))
        .go();
  }

  /// 根据日期范围获取健康记录
  Stream<List<HealthRecordRow>> getHealthRecordsByDateRange(
      DateTime startDate, DateTime endDate) {
    return (_database.select(_database.healthRecords)
          ..where((t) => t.createdAt.isBetweenValues(startDate, endDate))
          ..orderBy([(t) => OrderingTerm.desc(t.createdAt)]))
        .watch();
  }

  /// 根据类型获取健康记录
  Stream<List<HealthRecordRow>> getHealthRecordsByType(
      HealthRecordTypeEnum type) {
    return (_database.select(_database.healthRecords)
          ..where((t) => t.type.equals(type.toInt()))
          ..orderBy([(t) => OrderingTerm.desc(t.createdAt)]))
        .watch();
  }

  /// 获取最近的健康记录
  Future<HealthRecordRow?> getLatestHealthRecord() {
    return (_database.select(_database.healthRecords)
          ..orderBy([(t) => OrderingTerm.desc(t.createdAt)])
          ..limit(1))
        .getSingleOrNull();
  }

  /// 获取最近的血压记录
  Future<HealthRecordRow?> getLatestBloodPressureRecord() {
    return (_database.select(_database.healthRecords)
          ..where((t) => t.type.equals(HealthRecordTypeEnum.bloodPressure.toInt()))
          ..orderBy([(t) => OrderingTerm.desc(t.createdAt)])
          ..limit(1))
        .getSingleOrNull();
  }

  /// 获取最近的血糖记录
  Future<HealthRecordRow?> getLatestBloodSugarRecord() {
    return (_database.select(_database.healthRecords)
          ..where((t) => t.type.equals(HealthRecordTypeEnum.bloodSugar.toInt()))
          ..orderBy([(t) => OrderingTerm.desc(t.createdAt)])
          ..limit(1))
        .getSingleOrNull();
  }

  /// 分页获取健康记录
  Future<List<HealthRecordRow>> getHealthRecordsPaginated({
    required int page,
    required int pageSize,
  }) async {
    final offset = (page - 1) * pageSize;
    return (_database.select(_database.healthRecords)
          ..orderBy([(t) => OrderingTerm.desc(t.createdAt)])
          ..limit(pageSize, offset: offset))
        .get();
  }

  /// 获取今天的血压记录
  Future<List<HealthRecordRow>> getTodayBloodPressureRecords() async {
    final now = DateTime.now();
    final todayStart = DateTime(now.year, now.month, now.day);
    final todayEnd = todayStart.add(const Duration(days: 1));

    return (_database.select(_database.healthRecords)
          ..where((t) => t.type.equals(HealthRecordTypeEnum.bloodPressure.toInt()))
          ..where((t) => t.createdAt.isBetweenValues(todayStart, todayEnd))
          ..orderBy([(t) => OrderingTerm.desc(t.createdAt)]))
        .get();
  }

  /// 获取今天的血糖记录
  Future<List<HealthRecordRow>> getTodayBloodSugarRecords() async {
    final now = DateTime.now();
    final todayStart = DateTime(now.year, now.month, now.day);
    final todayEnd = todayStart.add(const Duration(days: 1));

    return (_database.select(_database.healthRecords)
          ..where((t) => t.type.equals(HealthRecordTypeEnum.bloodSugar.toInt()))
          ..where((t) => t.createdAt.isBetweenValues(todayStart, todayEnd))
          ..orderBy([(t) => OrderingTerm.desc(t.createdAt)]))
        .get();
  }
}

/// 统一健康记录仓库提供者
@riverpod
HealthRecordRepository healthRecordRepository(Ref ref) {
  final database = ref.watch(databaseProvider);
  return HealthRecordRepository(database);
}
