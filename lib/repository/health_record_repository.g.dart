// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'health_record_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$healthRecordRepositoryHash() =>
    r'd4db4c8ab607483671b616657472b654427e32f2';

/// 统一健康记录仓库提供者
///
/// Copied from [healthRecordRepository].
@ProviderFor(healthRecordRepository)
final healthRecordRepositoryProvider =
    AutoDisposeProvider<HealthRecordRepository>.internal(
      healthRecordRepository,
      name: r'healthRecordRepositoryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$healthRecordRepositoryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef HealthRecordRepositoryRef =
    AutoDisposeProviderRef<HealthRecordRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
