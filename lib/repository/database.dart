import 'dart:io';

import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;

import '../model/blood_pressure.dart';
import '../model/blood_sugar.dart';
import '../model/health_record.dart';

part 'database.g.dart';

/// 健康日记数据库
@DriftDatabase(tables: [BloodPressures, BloodSugars, HealthRecords])
class HealthDiaryDatabase extends _$HealthDiaryDatabase {
  HealthDiaryDatabase() : super(_openConnection());
  HealthDiaryDatabase.forTesting(super.executor);

  @override
  int get schemaVersion => 2;

  @override
  MigrationStrategy get migration => MigrationStrategy(
    onCreate: (m) async {
      await m.createAll();
    },
    onUpgrade: (m, from, to) async {
      if (from < 2) {
        // 添加新的健康记录表
        await m.createTable(healthRecords);

        // 迁移现有数据
        await _migrateExistingData();
      }
    },
  );

  /// 迁移现有数据到新表
  Future<void> _migrateExistingData() async {
    // 迁移血压数据
    final bloodPressures = await select(this.bloodPressures).get();
    for (final bp in bloodPressures) {
      await into(healthRecords).insert(HealthRecordsCompanion(
        type: Value(HealthRecordTypeEnum.bloodPressure.toInt()),
        data: Value('{"systolic":${bp.systolic},"diastolic":${bp.diastolic},"pulse":${bp.pulse},"note":"${bp.note ?? ""}"}'),
        createdAt: Value(bp.createdAt),
      ));
    }

    // 迁移血糖数据
    final bloodSugars = await select(this.bloodSugars).get();
    for (final bs in bloodSugars) {
      await into(healthRecords).insert(HealthRecordsCompanion(
        type: Value(HealthRecordTypeEnum.bloodSugar.toInt()),
        data: Value('{"value":${bs.value},"note":"${bs.note ?? ""}"}'),
        createdAt: Value(bs.createdAt),
      ));
    }
  }

  /// 打开数据库连接
  static LazyDatabase _openConnection() {
    return LazyDatabase(() async {
      final dbFolder = await getApplicationDocumentsDirectory();
      final file = File(p.join(dbFolder.path, 'health_diary.sqlite'));
      debugPrint('db path: ${file.path}');
      return NativeDatabase.createInBackground(file);
    });
  }
}
