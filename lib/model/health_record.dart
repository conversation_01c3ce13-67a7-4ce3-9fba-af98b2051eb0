import 'dart:convert';
import 'package:drift/drift.dart';
import '../types/health_types.dart';

/// 健康记录数据库表定义
class HealthRecords extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get type => integer()(); // 记录类型（枚举值）
  TextColumn get data => text()(); // JSON数据
  DateTimeColumn get createdAt => dateTime()(); // 创建时间
}

/// 健康记录行类
class HealthRecordRow {
  final int id;
  final HealthRecordTypeEnum type;
  final HealthRecordData model;
  final DateTime createdAt;

  const HealthRecordRow({
    required this.id,
    required this.type,
    required this.model,
    required this.createdAt,
  });

  /// 从数据库记录构造
  factory HealthRecordRow.fromDbRecord({
    required int id,
    required int type,
    required String data,
    required DateTime createdAt,
  }) {
    final recordType = HealthRecordTypeEnum.fromInt(type);
    final jsonData = json.decode(data) as Map<String, dynamic>;
    
    final HealthRecordData model;
    switch (recordType) {
      case HealthRecordTypeEnum.bloodPressure:
        model = HealthRecordData.fromJson(jsonData);
        break;
      case HealthRecordTypeEnum.bloodSugar:
        model = HealthRecordData.fromJson(jsonData);
        break;
    }

    return HealthRecordRow(
      id: id,
      type: recordType,
      model: model,
      createdAt: createdAt,
    );
  }

  /// 转换为数据库记录
  Map<String, dynamic> toDbRecord() {
    return {
      'id': id,
      'type': type.toInt(),
      'data': json.encode(model.toJson()),
      'created_at': createdAt,
    };
  }
}
