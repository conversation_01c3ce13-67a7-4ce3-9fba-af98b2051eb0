import 'dart:convert';
import 'package:drift/drift.dart';
import '../types/health_types.dart';

/// 健康记录数据库表定义
@UseRowClass(HealthRecordRow)
class HealthRecords extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get type => integer()(); // 记录类型（枚举值）
  TextColumn get data => text()(); // JSON数据
  DateTimeColumn get createdAt => dateTime()(); // 创建时间
}

/// 健康记录行类
class HealthRecordRow {
  final int id;
  final HealthRecordTypeEnum type;
  final HealthRecordData model;
  final DateTime createdAt;

  /// Drift UseRowClass 构造函数
  HealthRecordRow({
    required this.id,
    required int type,
    required String data,
    required this.createdAt,
  }) : type = HealthRecordTypeEnum.fromInt(type),
       model = _parseData(HealthRecordTypeEnum.fromInt(type), data);

  /// 解析 JSON 数据
  static HealthRecordData _parseData(HealthRecordTypeEnum type, String data) {
    final jsonData = json.decode(data) as Map<String, dynamic>;

    switch (type) {
      case HealthRecordTypeEnum.bloodPressure:
        return HealthRecordData.bloodPressure(
          systolic: jsonData['systolic'] as int,
          diastolic: jsonData['diastolic'] as int,
          pulse: jsonData['pulse'] as int?,
          note: jsonData['note'] as String?,
        );
      case HealthRecordTypeEnum.bloodSugar:
        return HealthRecordData.bloodSugar(
          value: jsonData['value'] as double,
          note: jsonData['note'] as String?,
        );
    }
  }

  /// 转换为数据库记录
  Map<String, dynamic> toDbRecord() {
    return {
      'id': id,
      'type': type.toInt(),
      'data': json.encode(model.toJson()),
      'created_at': createdAt,
    };
  }
}
