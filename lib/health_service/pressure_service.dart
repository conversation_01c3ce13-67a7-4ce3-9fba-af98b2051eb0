import 'package:health_diary/repository/blood_pressure_repository.dart';
import 'package:health_diary/types/health_id_record.dart';
import 'package:health_diary/types/health_record.dart';

import 'health_service.dart';

class PressureService extends HealService<BloodPressureRepository> {
  @override
  bool canScan() => true;

  @override
  Future<Map<int, HealthRecordEntry>> parseRecordEntry(
    List<HealthIdRecord> record,
    BloodPressureRepository repo,
  ) {
    // TODO: implement parseRecordEntry
    throw UnimplementedError();
  }

  @override
  String homeQuerySql() {
    // TODO: implement homeQuerySql
    throw UnimplementedError();
  }
}
