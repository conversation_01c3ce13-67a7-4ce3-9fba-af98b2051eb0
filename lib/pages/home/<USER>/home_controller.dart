import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../../types/health_record.dart';
import '../../../repository/blood_pressure_repository.dart';
import '../../../repository/blood_sugar_repository.dart';

part 'home_controller.g.dart';

/// 首页控制器
@riverpod
class HomeController extends _$HomeController {
  @override
  Future<TodayHealthOverview> build() async {
    return await _loadTodayHealthOverview();
  }

  /// 加载今日健康概览数据
  Future<TodayHealthOverview> _loadTodayHealthOverview() async {
    try {
      final bloodPressureRepo = ref.read(bloodPressureRepositoryProvider);
      final bloodSugarRepo = ref.read(bloodSugarRepositoryProvider);

      // 获取今天的开始和结束时间
      final now = DateTime.now();
      final todayStart = DateTime(now.year, now.month, now.day);
      final todayEnd = todayStart.add(const Duration(days: 1));

      // 获取今天的血压记录
      final todayBloodPressures = await bloodPressureRepo
          .getBloodPressuresByDateRange(todayStart, todayEnd)
          .first;

      // 获取今天的血糖记录
      final todayBloodSugars = await bloodSugarRepo
          .getBloodSugarsByDateRange(todayStart, todayEnd)
          .first;

      // 计算血压平均值
      String? avgBloodPressure;
      bool hasBloodPressureRecord = todayBloodPressures.isNotEmpty;
      if (hasBloodPressureRecord) {
        final avgSystolic = todayBloodPressures
            .map((bp) => bp.systolic)
            .reduce((a, b) => a + b) / todayBloodPressures.length;
        final avgDiastolic = todayBloodPressures
            .map((bp) => bp.diastolic)
            .reduce((a, b) => a + b) / todayBloodPressures.length;
        avgBloodPressure = '${avgSystolic.round()}/${avgDiastolic.round()}';
      }

      // 计算血糖平均值
      String? avgBloodSugar;
      bool hasBloodSugarRecord = todayBloodSugars.isNotEmpty;
      if (hasBloodSugarRecord) {
        final avgSugar = todayBloodSugars
            .map((bs) => bs.value)
            .reduce((a, b) => a + b) / todayBloodSugars.length;
        avgBloodSugar = avgSugar.toStringAsFixed(1);
      }

      // 确定健康状态
      String status = _determineHealthStatus(
        hasBloodPressureRecord: hasBloodPressureRecord,
        hasBloodSugarRecord: hasBloodSugarRecord,
        avgSystolic: hasBloodPressureRecord ?
            todayBloodPressures.map((bp) => bp.systolic).reduce((a, b) => a + b) / todayBloodPressures.length : null,
        avgDiastolic: hasBloodPressureRecord ?
            todayBloodPressures.map((bp) => bp.diastolic).reduce((a, b) => a + b) / todayBloodPressures.length : null,
        avgSugar: hasBloodSugarRecord ?
            todayBloodSugars.map((bs) => bs.value).reduce((a, b) => a + b) / todayBloodSugars.length : null,
      );

      return TodayHealthOverview.withRecords(
        status: status,
        bloodPressure: avgBloodPressure,
        bloodSugar: avgBloodSugar,
        hasBloodPressureRecord: hasBloodPressureRecord,
        hasBloodSugarRecord: hasBloodSugarRecord,
      );
    } catch (e) {
      // 出错时返回无记录状态
      return TodayHealthOverview.noRecords();
    }
  }

  /// 确定健康状态
  String _determineHealthStatus({
    required bool hasBloodPressureRecord,
    required bool hasBloodSugarRecord,
    double? avgSystolic,
    double? avgDiastolic,
    double? avgSugar,
  }) {
    // 如果没有任何记录
    if (!hasBloodPressureRecord && !hasBloodSugarRecord) {
      return 'no_records_today'.tr();
    }

    // 简单的健康状态判断逻辑
    bool isBloodPressureNormal = true;
    bool isBloodSugarNormal = true;

    if (hasBloodPressureRecord && avgSystolic != null && avgDiastolic != null) {
      // 正常血压范围：收缩压 < 120 且舒张压 < 80
      isBloodPressureNormal = avgSystolic < 120 && avgDiastolic < 80;
    }

    if (hasBloodSugarRecord && avgSugar != null) {
      // 正常血糖范围：3.9-6.1 mmol/L（空腹）
      isBloodSugarNormal = avgSugar >= 3.9 && avgSugar <= 6.1;
    }

    if (isBloodPressureNormal && isBloodSugarNormal) {
      return 'health_status_good'.tr();
    } else if (!isBloodPressureNormal && !isBloodSugarNormal) {
      return 'health_status_attention'.tr();
    } else {
      return 'health_status_fair'.tr();
    }
  }

  /// 刷新数据
  void refresh() {
    ref.invalidateSelf();
  }
}


