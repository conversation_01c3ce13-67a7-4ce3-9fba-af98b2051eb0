import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:easy_localization/easy_localization.dart';

import 'package:health_diary/themes/app_theme.dart';
import 'package:health_diary/types/health_record.dart';
import 'health_metric_widget.dart';

/// 今日健康概览卡片
class TodayHealthCard extends StatelessWidget {
  final TodayHealthOverview overview;

  const TodayHealthCard({super.key, required this.overview});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondary,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('today_health'.tr(), style: Theme.of(context).textTheme.titleMedium),
              FaIcon(FontAwesomeIcons.heartPulse, color: Theme.of(context).colorScheme.onSurfaceVariant, size: 20),
            ],
          ),
          const SizedBox(height: 12),
          Text(_getLocalizedStatus(overview.status), style: Theme.of(context).textTheme.headlineMedium),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: _buildHealthMetric(
                  context: context,
                  label: 'blood_pressure'.tr(),
                  value: overview.bloodPressure,
                  unit: overview.bloodPressureUnit,
                  hasRecord: overview.hasBloodPressureRecord,
                  icon: FaIcon(
                    FontAwesomeIcons.heartPulse,
                    size: 16,
                    color: context.appColors.heartIcon.withValues(alpha: 0.6),
                  ),
                ),
              ),
              const SizedBox(width: 24),
              Expanded(
                child: _buildHealthMetric(
                  context: context,
                  label: 'blood_sugar'.tr(),
                  value: overview.bloodSugar,
                  unit: overview.bloodSugarUnit,
                  hasRecord: overview.hasBloodSugarRecord,
                  icon: FaIcon(
                    FontAwesomeIcons.droplet,
                    size: 16,
                    color: context.appColors.dropletIcon.withValues(alpha: 0.6),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  /// 构建健康指标 Widget
  Widget _buildHealthMetric({
    required BuildContext context,
    required String label,
    required String? value,
    required String unit,
    required bool hasRecord,
    required Widget icon,
  }) {
    if (hasRecord && value != null) {
      return HealthMetricWidget(
        label: label,
        value: value,
        unit: unit,
        icon: icon,
      );
    } else {
      return _NoRecordMetricWidget(
        label: label,
        icon: icon,
      );
    }
  }

  String _getLocalizedStatus(String status) {
    // 直接返回状态，因为在 controller 中已经做了国际化处理
    return status;
  }
}

/// 无记录的指标 Widget
class _NoRecordMetricWidget extends StatelessWidget {
  final String label;
  final Widget icon;

  const _NoRecordMetricWidget({
    required this.label,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.labelMedium,
              ),
              icon,
            ],
          ),
          const SizedBox(height: 4),
          Text(
            'no_record_today'.tr(),
            style: Theme.of(context).textTheme.displayMedium?.copyWith(
              color: Theme.of(context).textTheme.displayMedium?.color?.withValues(alpha: 0.5),
            ),
          ),
          Text(
            '',  // 空的单位文本，保持布局一致
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ),
    );
  }
}
