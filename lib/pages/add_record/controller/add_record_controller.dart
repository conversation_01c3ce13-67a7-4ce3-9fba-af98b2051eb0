import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:health_diary/pages/add_record/controller/data_controller.dart';
import 'package:health_diary/providers/scan_service/scan_provider.dart';
import 'package:health_diary/repository/health_record_repository.dart';
import 'package:health_diary/types/health_types.dart';

import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'add_record_controller.g.dart';
part 'add_record_controller.freezed.dart';

enum AddRecordInputType { scan, manual }

@freezed
abstract class AddRecordState with _$AddRecordState {
  factory AddRecordState({
    required AddRecordInputType inputType,
    required HealthRecordTypeEnum recordType,
    @Default(false) bool hasScanError,
  }) = _AddRecordState;
}

@riverpod
class AddRecordController extends _$AddRecordController {
  @override
  AddRecordState build() {
    ref.watch(dataControllerProvider.notifier);
    return AddRecordState(inputType: AddRecordInputType.scan, recordType: HealthRecordTypeEnum.bloodPressure);
  }

  void clearScanError() {
    state = state.copyWith(hasScanError: false);
  }

  void setInputType(AddRecordInputType inputType) {
    state = state.copyWith(inputType: inputType);
  }

  void setRecordType(HealthRecordTypeEnum recordType) {
    state = state.copyWith(recordType: recordType);
  }

  /// 扫描数据，成功后跳转手动输入页面
  Future<void> scan({required Uint8List imageData}) async {
    // 清除之前的错误状态
    state = state.copyWith(hasScanError: false);

    final List<int> result = await ScanService.scan(imageData: imageData);
    debugPrint('Scan result: $result');

    if (result.isEmpty) {
      // 设置扫描错误状态
      state = state.copyWith(hasScanError: true);
      return;
    }

    final dataController = ref.read(dataControllerProvider.notifier);
    final parseResult = dataController.parseScanData(result, state.recordType);

    if (!parseResult) {
      // 设置扫描错误状态
      state = state.copyWith(hasScanError: true);
    }

    state = state.copyWith(inputType: AddRecordInputType.manual);

    return;
  }

  /// 保存血压记录到数据库
  Future<bool> saveBloodPressureRecord() async {
    try {
      final dataState = ref.read(dataControllerProvider);
      final pressureRecord = dataState.pressure;

      if (pressureRecord == null) {
        debugPrint('No blood pressure record to save');
        return false;
      }

      final repository = ref.read(healthRecordRepositoryProvider);

       final id = await repository.addBloodPressureRecord(
         systolic: pressureRecord.systolic,
         diastolic: pressureRecord.diastolic,
         pulse: pressureRecord.pulse,
         note: pressureRecord.note,
         createdAt: pressureRecord.time,
       );
      debugPrint('Blood pressure record saved with id: $id');

      return true;
    } catch (e) {
      debugPrint('Error saving blood pressure record: $e');
      return false;
    }
  }

  /// 保存血糖记录到数据库
  Future<bool> saveBloodSugarRecord() async {
    try {
      final dataState = ref.read(dataControllerProvider);
      final sugarRecord = dataState.sugar;

      if (sugarRecord == null) {
        debugPrint('No blood sugar record to save');
        return false;
      }

      final repository = ref.read(healthRecordRepositoryProvider);

       final id = await repository.addBloodSugarRecord(
         value: sugarRecord.value,
         note: sugarRecord.note,
         createdAt: sugarRecord.time,
       );
      debugPrint('Blood sugar record saved with id: $id');

      return true;
    } catch (e) {
      debugPrint('Error saving blood sugar record: $e');
      return false;
    }
  }
}
